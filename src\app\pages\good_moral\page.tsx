"use client";

import { useState } from "react";
import <PERSON><PERSON><PERSON><PERSON> from "pizzip";
import Docxtemplater from "docxtemplater";
import { saveAs } from "file-saver";
import mammoth from "mammoth";
// import ImageModule from "docxtemplater-image-module-free";

export default function GoodMoralPage() {
  const [form, setForm] = useState({
    name: "",
    date: "",
    address: "",
    image: "", // base64 image data
  });

  const [previewHTML, setPreviewHTML] = useState<string>("");
  const [isLoading, setIsLoading] = useState(false);

  // Test function to create a simple template
  const createTestTemplate = async () => {
    try {
      // Create a simple test document
      const testContent = `Name: {{name}}
Date: {{date}}
Place: {{address}}
Image: {{image}}`;

      alert(
        `TEMPLATE CREATION GUIDE:\n\n${testContent}\n\nIMPORTANT STEPS:\n1. Open Microsoft Word\n2. Create a new blank document\n3. Type the placeholders EXACTLY as shown above\n4. DO NOT copy-paste the placeholders\n5. Type them manually to avoid XML splitting\n6. Save as example.docx in your public folder\n\nCOMMON ISSUE: Word sometimes splits {{name}} into separate XML runs like {{na}}{{me}}, causing duplicate tags.`
      );
    } catch (err) {
      console.error("Error:", err);
    }
  };

  const handleTextChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = () => {
      const base64 = (reader.result as string).split(",")[1]; // remove base64 header
      setForm((prev) => ({ ...prev, image: base64 }));
    };
    reader.readAsDataURL(file);
  };

  const generateDocument = async () => {
    if (!form.name || !form.date || !form.address) {
      alert("Please fill in all required fields");
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch("/example.docx");
      if (!response.ok) {
        throw new Error("Failed to load template document");
      }
      const content = await response.arrayBuffer();
      console.log("Template loaded, size:", content.byteLength);

      let zip;
      try {
        zip = new PizZip(content);
        console.log("PizZip created successfully");
      } catch (zipError) {
        console.error("Error creating PizZip:", zipError);
        alert(
          "Error: The template file appears to be corrupted or not a valid Word document."
        );
        return;
      }

      // Try without image module first to isolate the issue
      const modules: any[] = [];

      // Only add image module if image is provided and we want to test it
      // Commenting out for now to debug the basic template first
      /*
      if (form.image) {
        const imageOpts = {
          centered: false,
          getImage(tagValue: string) {
            return Uint8Array.from(atob(tagValue), (c) => c.charCodeAt(0));
          },
          getSize(): [number, number] {
            return [100, 100]; // size in pixels
          },
        };
        const imageModule = new ImageModule(imageOpts);
        modules.push(imageModule);
      }
      */

      // First, let's try to create a basic Docxtemplater without any modules
      let doc;
      try {
        console.log("Creating Docxtemplater instance...");
        doc = new Docxtemplater(zip, {
          modules: modules,
          paragraphLoop: true,
          linebreaks: true,
          errorLogging: true, // Enable error logging
        });
        console.log("Docxtemplater created successfully");
      } catch (error: any) {
        console.error("Docxtemplater creation error:", error);
        console.error("Error details:", {
          name: error.name,
          message: error.message,
          properties: error.properties,
          stack: error.stack,
        });

        if (error.properties && error.properties.errors) {
          console.error("Template errors:", error.properties.errors);
          const errorMessages = error.properties.errors
            .map((err: any) => {
              console.log("Individual error:", err);

              // Check for duplicate tag errors
              if (err.message && err.message.includes("duplicate")) {
                return `DUPLICATE TAG ERROR: ${err.message}\nLocation: ${
                  err.part || "Unknown"
                }\n\nSOLUTION: Your Word document has split placeholders. Please:\n1. Delete the current placeholder\n2. Type it again manually (don't copy-paste)\n3. Make sure to type {{${
                  err.part
                }}} as one continuous string`;
              }

              return `Error: ${err.message || "Unknown error"}\nLocation: ${
                err.part || "Unknown location"
              }\nType: ${err.name || "Unknown type"}`;
            })
            .join("\n\n");

          alert(
            `TEMPLATE PARSING ERRORS:\n\n${errorMessages}\n\nCOMMON FIX FOR DUPLICATE TAGS:\n1. Open your example.docx\n2. Delete ALL placeholders\n3. Type them manually: {{name}}, {{date}}, {{address}}, {{image}}\n4. DO NOT copy-paste placeholders\n5. Save the document`
          );
        } else {
          alert(
            `Error parsing document template: ${error.message}\n\nPlease ensure:\n1. The file is a valid .docx document\n2. Placeholders use {{variable}} syntax\n3. The document is not password protected\n4. Placeholders are typed manually (not copy-pasted)`
          );
        }
        return;
      }

      // Set data - for now, replace image placeholder with text if no image
      const templateData = {
        name: form.name,
        date: form.date,
        address: form.address,
        image: form.image || "[No image provided]", // Fallback text for image placeholder
      };

      doc.setData(templateData);

      try {
        doc.render();
      } catch (error: any) {
        console.error("Render error:", error);
        if (error.properties && error.properties.errors) {
          console.error("Render errors:", error.properties.errors);
          const errorMessages = error.properties.errors
            .map(
              (err: any) =>
                `${err.message} at ${err.part || "unknown location"}`
            )
            .join("\n");
          alert(`Document rendering errors:\n${errorMessages}`);
        } else {
          alert("Error rendering document. Please check your input data.");
        }
        return;
      }

      const outputBlob = doc.getZip().generate({ type: "blob" });

      // Preview with mammoth
      const { value } = await mammoth.convertToHtml({
        arrayBuffer: await outputBlob.arrayBuffer(),
      });
      setPreviewHTML(value);

      // Download the document
      saveAs(outputBlob, "good-moral-certificate.docx");
    } catch (err) {
      console.error("Error generating document:", err);
      alert("Error generating document. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">
        Good Moral Certificate Generator
      </h1>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Input Form */}
        <div className="space-y-4">
          <h2 className="text-lg font-semibold mb-4">Fill in the details</h2>

          <div>
            <label htmlFor="name" className="block text-sm font-medium mb-2">
              Name *
            </label>
            <input
              id="name"
              type="text"
              name="name"
              placeholder="Enter full name"
              value={form.name}
              onChange={handleTextChange}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            />
          </div>

          <div>
            <label htmlFor="date" className="block text-sm font-medium mb-2">
              Date *
            </label>
            <input
              id="date"
              type="date"
              name="date"
              value={form.date}
              onChange={handleTextChange}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            />
          </div>

          <div>
            <label htmlFor="address" className="block text-sm font-medium mb-2">
              Place *
            </label>
            <input
              id="address"
              type="text"
              name="address"
              placeholder="Enter place/address"
              value={form.address}
              onChange={handleTextChange}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            />
          </div>

          <div>
            <label htmlFor="image" className="block text-sm font-medium mb-2">
              Image (optional)
            </label>
            <input
              id="image"
              type="file"
              accept="image/*"
              onChange={handleImageUpload}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            {form.image && (
              <p className="text-sm text-green-600 mt-1">
                Image uploaded successfully
              </p>
            )}
          </div>

          <div className="space-y-2">
            <button
              onClick={generateDocument}
              disabled={isLoading}
              className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-medium py-2 px-4 rounded-md transition-colors"
            >
              {isLoading ? "Generating..." : "Generate & Download Document"}
            </button>

            <button
              onClick={createTestTemplate}
              className="w-full bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded-md transition-colors"
            >
              Show Expected Template Format
            </button>
          </div>
        </div>

        {/* Preview */}
        <div className="space-y-4">
          <h2 className="text-lg font-semibold mb-4">Document Preview</h2>

          {previewHTML ? (
            <div className="border border-gray-300 rounded-md p-4 bg-white min-h-[400px] overflow-auto">
              <div dangerouslySetInnerHTML={{ __html: previewHTML }} />
            </div>
          ) : (
            <div className="border border-gray-300 rounded-md p-4 bg-gray-50 min-h-[400px] flex items-center justify-center text-gray-500">
              Document preview will appear here after generation
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
