"use client";

import { useState } from "react";
import <PERSON><PERSON><PERSON><PERSON> from "pizzip";
import Docxtemplater from "docxtemplater";
import { saveAs } from "file-saver";
import mammoth from "mammoth";
import ImageModule from "docxtemplater-image-module-free";

export default function GoodMoralPage() {
  const [form, setForm] = useState({
    name: "",
    date: "",
    address: "",
    image: "", // base64 image data
  });

  const [previewHTML, setPreviewHTML] = useState<string>("");
  const [isLoading, setIsLoading] = useState(false);

  const handleTextChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = () => {
      const base64 = (reader.result as string).split(",")[1]; // remove base64 header
      setForm((prev) => ({ ...prev, image: base64 }));
    };
    reader.readAsDataURL(file);
  };

  const generateDocument = async () => {
    if (!form.name || !form.date || !form.address) {
      alert("Please fill in all required fields");
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch("/example.docx");
      if (!response.ok) {
        throw new Error("Failed to load template document");
      }
      const content = await response.arrayBuffer();

      const zip = new PizZip(content);

      // Image module config
      const imageOpts = {
        centered: false,
        getImage(tagValue: string) {
          return Uint8Array.from(atob(tagValue), (c) => c.charCodeAt(0));
        },
        getSize(): [number, number] {
          return [100, 100]; // size in pixels
        },
      };

      const imageModule = new ImageModule(imageOpts);

      const doc = new Docxtemplater(zip, {
        modules: [imageModule],
        paragraphLoop: true,
        linebreaks: true,
      });

      doc.setData({
        name: form.name,
        date: form.date,
        address: form.address,
        image: form.image,
      });

      doc.render();

      const outputBlob = doc.getZip().generate({ type: "blob" });

      // Preview with mammoth
      const { value } = await mammoth.convertToHtml({
        arrayBuffer: await outputBlob.arrayBuffer(),
      });
      setPreviewHTML(value);

      // Download the document
      saveAs(outputBlob, "good-moral-certificate.docx");
    } catch (err) {
      console.error("Error generating document:", err);
      alert("Error generating document. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">
        Good Moral Certificate Generator
      </h1>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Input Form */}
        <div className="space-y-4">
          <h2 className="text-lg font-semibold mb-4">Fill in the details</h2>

          <div>
            <label htmlFor="name" className="block text-sm font-medium mb-2">
              Name *
            </label>
            <input
              id="name"
              type="text"
              name="name"
              placeholder="Enter full name"
              value={form.name}
              onChange={handleTextChange}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            />
          </div>

          <div>
            <label htmlFor="date" className="block text-sm font-medium mb-2">
              Date *
            </label>
            <input
              id="date"
              type="date"
              name="date"
              value={form.date}
              onChange={handleTextChange}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            />
          </div>

          <div>
            <label htmlFor="address" className="block text-sm font-medium mb-2">
              Place *
            </label>
            <input
              id="address"
              type="text"
              name="address"
              placeholder="Enter place/address"
              value={form.address}
              onChange={handleTextChange}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            />
          </div>

          <div>
            <label htmlFor="image" className="block text-sm font-medium mb-2">
              Image (optional)
            </label>
            <input
              id="image"
              type="file"
              accept="image/*"
              onChange={handleImageUpload}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            {form.image && (
              <p className="text-sm text-green-600 mt-1">
                Image uploaded successfully
              </p>
            )}
          </div>

          <button
            onClick={generateDocument}
            disabled={isLoading}
            className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-medium py-2 px-4 rounded-md transition-colors"
          >
            {isLoading ? "Generating..." : "Generate & Download Document"}
          </button>
        </div>

        {/* Preview */}
        <div className="space-y-4">
          <h2 className="text-lg font-semibold mb-4">Document Preview</h2>

          {previewHTML ? (
            <div className="border border-gray-300 rounded-md p-4 bg-white min-h-[400px] overflow-auto">
              <div dangerouslySetInnerHTML={{ __html: previewHTML }} />
            </div>
          ) : (
            <div className="border border-gray-300 rounded-md p-4 bg-gray-50 min-h-[400px] flex items-center justify-center text-gray-500">
              Document preview will appear here after generation
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
